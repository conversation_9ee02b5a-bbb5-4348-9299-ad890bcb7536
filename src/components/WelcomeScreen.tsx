import React from "react";
import {
  clearCookieConsent,
  getCookieConsentStatus,
} from "../utils/cookieUtils";
import { PrimaryButton } from "microapps";

interface WelcomeScreenProps {
  onStartGame: () => void;
  onShowRules: () => void;
  onGameEnd: (won: boolean) => void;
  aiLoading: boolean;
}

/**
 * Welcome Screen Component
 *
 * Displays the main menu when cookies have already been accepted.
 * Features:
 * - Welcome message for returning users
 * - Game start button
 * - Rules button
 * - Test buttons for development
 */
export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onStartGame,
  onShowRules,
  onGameEnd,
  aiLoading,
}) => {
  /**
   * Handle clearing cookie consent for testing purposes
   */
  const handleClearCookies = () => {
    if (clearCookieConsent()) {
      alert(
        "🍪 Cookies limpiadas. Recarga la página para ver el banner de cookies nuevamente."
      );
    }
  };

  return (
    <div className="card">
      <div className="game-container">
        <h1 className="welcome-title title1 bold">
          El velo del misterio se alza.
        </h1>

        <p className="welcome-body body1">
          ¿Estás listo para enfrentarte a Enygma y desvelar el personaje oculto
          en el que está pensando?
        </p>

        <PrimaryButton
          onClick={() => {}}
          text="Empezar"
          backgroundColor="#88FFD5"
          textColor="#001428"
          borderRadius="8px"
          spinnerColor="#000"
          className="welcome-button primary-button"
        />

        <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

        {/* Welcome Back Section */}
        <div className="quick-start-section">
          <h4 className="quick-start-title">👋 ¡Bienvenido de vuelta!</h4>
          <p className="quick-start-description">
            ¿Estás listo para poner a prueba tu ingenio? Se generará un
            personaje misterioso y tendrás que adivinarlo haciendo preguntas
            inteligentes. ¡Usa tu voz para interactuar con la IA y descubre
            quién se esconde detrás del misterio!
          </p>

          {/* Cookie status info for debugging */}
          <div
            style={{
              backgroundColor: "#e8f5e8",
              border: "1px solid #28a745",
              borderRadius: "6px",
              padding: "8px 12px",
              margin: "12px 0",
              fontSize: "14px",
              color: "#155724",
            }}
          >
            ✅ Estado de cookies: <strong>{getCookieConsentStatus()}</strong>
          </div>

          <div
            className="buttons-container"
            style={{
              display: "flex",
              gap: "12px",
              flexWrap: "wrap",
              justifyContent: "center",
            }}
          >
            <button
              onClick={onStartGame}
              disabled={aiLoading}
              className="primary-button"
              style={{ flex: "1", minWidth: "200px" }}
            >
              {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
            </button>

            <button
              onClick={onShowRules}
              className="secondary-button"
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "8px",
                padding: "12px 24px",
                fontSize: "16px",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.2s ease",
                flex: "0 0 auto",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#5a6268";
                e.currentTarget.style.transform = "translateY(-1px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#6c757d";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              📋 Ver Reglas
            </button>
          </div>

          {/* Temporary test buttons - Remove in production */}
          <div style={{ marginTop: "20px", textAlign: "center" }}>
            <small
              style={{ color: "#666", display: "block", marginBottom: "10px" }}
            >
              🧪 Botones de prueba (remover en producción):
            </small>
            <div
              style={{
                display: "flex",
                gap: "8px",
                justifyContent: "center",
                flexWrap: "wrap",
              }}
            >
              <button
                onClick={() => onGameEnd(true)}
                style={{
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                🎉 Simular Victoria
              </button>
              <button
                onClick={() => onGameEnd(false)}
                style={{
                  backgroundColor: "#dc3545",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                😔 Simular Derrota
              </button>
              <button
                onClick={handleClearCookies}
                style={{
                  backgroundColor: "#ffc107",
                  color: "#212529",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                  fontWeight: "600",
                }}
              >
                🍪 Limpiar Cookies
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
