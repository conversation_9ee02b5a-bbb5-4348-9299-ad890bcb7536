import { Modal } from "microapps";
import { startBackgroundMusic } from '../utils/audioUtils';

interface CookieBannerProps {
  onAccept: () => void;
}

export const CookieBanner: React.FC<CookieBannerProps> = ({ onAccept }) => {
  /**
   * Handle accept button click - play background music and proceed
   * Catches and logs any errors during music playback
   * @returns void
   */
  const handleAcceptAndPlay = async () => {
    try {
      await startBackgroundMusic('/assets/sounds/sound.mp3');
    } catch (error) {
      console.warn('⚠️ Error reproduciendo música de fondo:', error);
    }

    // Proceed with the original accept action
    onAccept();
  };

  return (
    <Modal
      title="Ayúdanos a mejorar"
      onClose={handleAcceptAndPlay}
      onCancel={handleAcceptAndPlay}
      onConfirm={handleAcceptAndPlay}
      cancelText="Cancelar"
      confirmText="Aceptar"
      body="Utilizamos cookies para ofrecerte un servicio más ágil y adaptado a tus preferencias."
    />
  );
};
